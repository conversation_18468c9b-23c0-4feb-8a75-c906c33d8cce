import torch
import torch.nn as nn
import pandas as pd
import numpy as np
import pickle

# ----------------------------
# 1. Load dataset (small sample for test)
# ----------------------------
df = pd.read_csv("../data/clean_dataset.csv")   # adjust path if needed
sample_df = df.sample(100, random_state=42)

# ----------------------------
# 2. Reload Tokenizer
# ----------------------------
class BPETokenizer:
    def __init__(self, vocab, merges=None):
        self.vocab = vocab
        self.word_to_idx = {w: i for i, w in enumerate(vocab)}
        self.idx_to_word = {i: w for w, i in self.word_to_idx.items()}
        self.merges = merges or []

    def encode(self, text):
        return list(text)  # fallback: char-level

    def decode(self, tokens):
        return "".join(tokens)

# Load saved tokenizer
with open("../bilstm/bpe_tokenizer.pkl", "rb") as f:
    tok_data = pickle.load(f)

if isinstance(tok_data, dict):
    tokenizer = BPETokenizer(
        vocab=tok_data.get("vocab", []),
        merges=tok_data.get("merges", [])
    )
else:
    tokenizer = tok_data

print("✅ Tokenizer loaded | Vocab size:", len(tokenizer.vocab))

# ----------------------------
# 3. Reload Model
# ----------------------------
class BiLSTMLM(nn.Module):
    def __init__(self, vocab_size, embed_dim=100, hidden_dim=128, num_layers=2):
        super().__init__()
        self.embed = nn.Embedding(vocab_size, embed_dim)
        self.lstm = nn.LSTM(embed_dim, hidden_dim, num_layers=num_layers,
                            batch_first=True, bidirectional=True)
        self.fc = nn.Linear(hidden_dim*2, vocab_size)
    def forward(self, x):
        x = self.embed(x)
        out, _ = self.lstm(x)
        out = self.fc(out)
        return out

device = "cuda" if torch.cuda.is_available() else "cpu"

checkpoint = torch.load("../bilstm/bilstm_model.pt", map_location=device)
print("📂 Checkpoint keys:", checkpoint.keys())

# Read model config from checkpoint
config = checkpoint.get("model_config", {})
print("🔧 Model config from checkpoint:", config)

# Build model with checkpoint config
model = BiLSTMLM(
    vocab_size=checkpoint["vocab_size"],
    embed_dim=config.get("embed_dim", 100),
    hidden_dim=config.get("hidden_dim", 128),
    num_layers=config.get("num_layers", 2)
).to(device)

# Load weights
model.load_state_dict(checkpoint["model_state_dict"])
model.eval()
print("✅ Model restored successfully on", device)

# ----------------------------
# 4. Perplexity Computation
# ----------------------------
def compute_perplexity(model, sequences, seq_len=20):
    model.eval()
    losses = []
    criterion = nn.CrossEntropyLoss()
    for seq in sequences[:200]:  # limit to 200 samples
        idxs = [tokenizer.word_to_idx[t] for t in seq if t in tokenizer.word_to_idx]
        for i in range(len(idxs) - seq_len):
            X = torch.tensor(idxs[i:i+seq_len], dtype=torch.long).unsqueeze(0).to(device)
            Y = torch.tensor(idxs[i+1:i+seq_len+1], dtype=torch.long).unsqueeze(0).to(device)
            with torch.no_grad():
                out = model(X)
                loss = criterion(out.view(-1, len(tokenizer.vocab)), Y.view(-1))
                losses.append(loss.item())
    avg_loss = np.mean(losses)
    return np.exp(avg_loss)

# Encode dataset with tokenizer
sequences = [tokenizer.encode(c) for c in sample_df['code'].astype(str)]
ppl = compute_perplexity(model, sequences)
print(f"\n📊 Model Perplexity: {ppl:.2f}")

# ----------------------------
# 5. Text Generation
# ----------------------------
def generate_text(model, start_token="d", length=100):
    model.eval()
    tokens = [tokenizer.word_to_idx.get(start_token, 0)]
    for _ in range(length):
        inp = torch.tensor(tokens[-20:], dtype=torch.long).unsqueeze(0).to(device)
        with torch.no_grad():
            out = model(inp)
            next_token = torch.argmax(out[0, -1]).item()
        tokens.append(next_token)
    return tokenizer.decode([tokenizer.idx_to_word[i] for i in tokens])

print("\n📝 Generated text sample:\n")
print(generate_text(model, start_token="d", length=200))
